import { Tabs } from "expo-router";
import { useEffect, useState } from "react";
import { Text, View } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRoles, isSuperAdmin, type UserRole } from "~/lib/utils";

export default function TabsLayout() {
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    async function loadUserData() {
      try {
        const roles = await getUserRoles();
        const adminStatus = await isSuperAdmin();
        setUserRoles(roles);
        setIsAdmin(adminStatus);
      } catch (error) {
        console.error("Error loading user data:", error);
      } finally {
        setLoading(false);
      }
    }

    loadUserData();
  }, []);

  if (loading) {
    return (
      <View
        className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? "bg-gray-900" : "bg-white"
        }`}
      >
        <Text
          className={`text-lg ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          Loading...
        </Text>
      </View>
    );
  }

  if (userRoles.length === 0) {
    return (
      <View
        className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? "bg-gray-900" : "bg-white"
        }`}
      >
        <Text
          className={`text-lg ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          No role found. Please sign in again.
        </Text>
      </View>
    );
  }

  const primaryRole = userRoles[0];

  // Render tabs based on user roles
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: isDarkColorScheme ? "#1f2937" : "#ffffff",
          borderTopColor: isDarkColorScheme ? "#374151" : "#e5e7eb",
        },
        tabBarActiveTintColor: isDarkColorScheme ? "#60a5fa" : "#3b82f6",
        tabBarInactiveTintColor: isDarkColorScheme ? "#9ca3af" : "#6b7280",
      }}
    >
      {/* Dashboard - visible to all roles */}
      <Tabs.Screen
        name="index"
        options={{
          title: "Dashboard",
          tabBarIcon: ({ color, size }) => (
            <Text style={{ color, fontSize: size }}>🏠</Text>
          ),
        }}
      />

      {/* Student tab - only visible to students */}
      {userRoles.includes("student") && (
        <Tabs.Screen
          name="student"
          options={{
            title: "Student",
            tabBarIcon: ({ color, size }) => (
              <Text style={{ color, fontSize: size }}>🎓</Text>
            ),
          }}
        />
      )}

      {/* Teacher tab - only visible to teachers */}
      {userRoles.includes("teacher") && (
        <Tabs.Screen
          name="teacher"
          options={{
            title: "Teacher",
            tabBarIcon: ({ color, size }) => (
              <Text style={{ color, fontSize: size }}>👨‍🏫</Text>
            ),
          }}
        />
      )}

      {/* HOD tab - only visible to HODs */}
      {userRoles.includes("HOD") && (
        <Tabs.Screen
          name="hod"
          options={{
            title: "HOD",
            tabBarIcon: ({ color, size }) => (
              <Text style={{ color, fontSize: size }}>👔</Text>
            ),
          }}
        />
      )}

      {/* Admin tab - only visible to super admins */}
      {isAdmin && (
        <Tabs.Screen
          name="admin"
          options={{
            title: "Admin",
            tabBarIcon: ({ color, size }) => (
              <Text style={{ color, fontSize: size }}>⚙️</Text>
            ),
          }}
        />
      )}
    </Tabs>
  );
}
