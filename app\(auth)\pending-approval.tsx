import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { Alert, Dimensions, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Clock } from "~/lib/icons/Clock";
import { Shield } from "~/lib/icons/Shield";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import { getProfile } from "~/lib/utils";

const { height } = Dimensions.get("window");

export default function PendingApproval() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [checking, setChecking] = useState(false);

  // Check verification status periodically
  useEffect(() => {
    const checkVerificationStatus = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const profile = await getProfile(user.id);
        if (profile?.isverify) {
          // User has been verified, redirect to main app
          router.replace("/(tabs)/");
        }
      } catch (error) {
        console.error("Error checking verification status:", error);
      }
    };

    // Check immediately
    checkVerificationStatus();

    // Check every 30 seconds
    const interval = setInterval(checkVerificationStatus, 30000);

    return () => clearInterval(interval);
  }, [router]);

  const handleCheckStatus = async () => {
    setChecking(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert("Error", "No authenticated user found");
        return;
      }

      const profile = await getProfile(user.id);
      if (profile?.isverify) {
        Alert.alert("Approved!", "Your account has been verified. Redirecting...", [
          {
            text: "Continue",
            onPress: () => router.replace("/(tabs)/"),
          },
        ]);
      } else {
        Alert.alert("Still Pending", "Your account is still pending approval. Please wait for a super admin to verify your profile.");
      }
    } catch (error) {
      console.error("Error checking status:", error);
      Alert.alert("Error", "Failed to check verification status");
    } finally {
      setChecking(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      router.replace("/(auth)/welcome");
    } catch (error) {
      console.error("Error signing out:", error);
      Alert.alert("Error", "Failed to sign out");
    }
  };

  return (
    <SafeAreaView className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}>
      <View 
        className="flex-1 px-6 justify-center"
        style={{ minHeight: height * 0.8 }}
      >
        {/* Icon */}
        <View className="items-center mb-8">
          <View className={`w-24 h-24 rounded-full items-center justify-center ${
            isDarkColorScheme ? 'bg-yellow-600' : 'bg-yellow-500'
          }`}>
            <Clock size={40} color="white" />
          </View>
        </View>

        {/* Content */}
        <View className="items-center mb-8">
          <Text className={`text-3xl font-bold text-center mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Pending Approval
          </Text>
          
          <Text className={`text-lg text-center mb-6 ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Your profile has been submitted and is awaiting verification by a super admin.
          </Text>

          <View className={`p-6 rounded-xl mb-6 ${
            isDarkColorScheme ? 'bg-gray-800 border border-gray-700' : 'bg-gray-50 border border-gray-200'
          }`}>
            <View className="flex-row items-center mb-4">
              <Shield size={20} color={isDarkColorScheme ? '#9CA3AF' : '#6B7280'} />
              <Text className={`ml-3 text-base font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-gray-900'
              }`}>
                What happens next?
              </Text>
            </View>
            
            <Text className={`text-base leading-6 ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              • A super admin will review your profile details{'\n'}
              • You'll receive access once approved{'\n'}
              • This usually takes 24-48 hours{'\n'}
              • You'll be notified via email when approved
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="space-y-4">
          <TouchableOpacity
            onPress={handleCheckStatus}
            disabled={checking}
            className={`w-full py-4 rounded-xl items-center ${
              checking 
                ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {checking ? "Checking..." : "Check Status"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleSignOut}
            className={`w-full py-4 rounded-xl items-center border-2 ${
              isDarkColorScheme 
                ? 'border-gray-600 bg-transparent' 
                : 'border-gray-300 bg-transparent'
            }`}
            activeOpacity={0.8}
          >
            <Text className={`font-semibold text-lg ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Sign Out
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
