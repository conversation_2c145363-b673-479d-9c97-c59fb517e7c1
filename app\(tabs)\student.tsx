import { useEffect, useState } from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRole } from "~/lib/utils";

export default function StudentScreen() {
  const { isDarkColorScheme } = useColorScheme();
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    async function loadRole() {
      const role = await getUserRole();
      setUserRole(role);
    }
    loadRole();
  }, []);

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
    >
      <ScrollView className="flex-1 px-6">
        {/* Header */}
        <View className="mt-6 mb-8">
          <Text className={`text-3xl font-bold ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Student Dashboard
          </Text>
          <Text className={`text-lg mt-2 ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Welcome back! Role: {userRole}
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Quick Actions
          </Text>
          <View className="space-y-4">
            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-blue-900'
              }`}>
                📚 View Courses
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                Check your enrolled courses and progress
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-green-900'
              }`}>
                📅 Schedule
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                View your class schedule and timings
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-purple-900'
              }`}>
                📊 Grades
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Check your academic performance
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Recent Activity
          </Text>
          <View className={`p-4 rounded-xl ${
            isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-50'
          }`}>
            <Text className={`text-base ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              • Course &quot;Mathematics 101&quot; assignment due in 2 days
            </Text>
            <Text className={`text-base mt-2 ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              • New grade posted for &quot;Physics Lab&quot;
            </Text>
            <Text className={`text-base mt-2 ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              • Upcoming exam: &quot;Chemistry&quot; on Friday
            </Text>
          </View>
        </View>

        {/* Stats */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            This Semester
          </Text>
          <View className="flex-row space-x-4">
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                5
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                Courses
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-green-400' : 'text-green-600'
              }`}>
                3.8
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                GPA
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-purple-400' : 'text-purple-600'
              }`}>
                85%
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Attendance
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
