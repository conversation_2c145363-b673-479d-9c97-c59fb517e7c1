import { useRouter } from "expo-router";
import React from "react";
import { Dimensions, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useColorScheme } from "~/lib/useColorScheme";

const { height } = Dimensions.get("window");

export default function Welcome() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
      style={{ paddingTop: height * 0.05 }}
    >
      <View className="flex-1 justify-center items-center px-8">
        {/* Logo/Brand Section */}
        <View className="items-center mb-16">
          <View className={`w-24 h-24 rounded-full items-center justify-center mb-6 ${
            isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500'
          }`}>
            <Text className="text-4xl font-bold text-white">A</Text>
          </View>
          <Text className={`text-3xl font-bold mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Welcome to Aevus
          </Text>
          <Text className={`text-lg text-center leading-6 ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Your secure and modern authentication experience
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="w-full space-y-4">
          <TouchableOpacity
            onPress={() => router.push("/(auth)/login")}
            className={`w-full py-4 rounded-xl items-center ${
              isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500'
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">Sign In</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push("/(auth)/signup")}
            className={`w-full py-4 rounded-xl items-center border-2 ${
              isDarkColorScheme 
                ? 'border-gray-600 bg-transparent' 
                : 'border-gray-300 bg-transparent'
            }`}
            activeOpacity={0.8}
          >
            <Text className={`font-semibold text-lg ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Create Account
            </Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View className="absolute bottom-8 items-center">
          <Text className={`text-sm ${
            isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
          }`}>
            By continuing, you agree to our Terms & Privacy Policy
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}
