import { useEffect, useState } from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRole } from "~/lib/utils";

export default function HODScreen() {
  const { isDarkColorScheme } = useColorScheme();
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    async function loadRole() {
      const role = await getUserRole();
      setUserRole(role);
    }
    loadRole();
  }, []);

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
    >
      <ScrollView className="flex-1 px-6">
        {/* Header */}
        <View className="mt-6 mb-8">
          <Text className={`text-3xl font-bold ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            HOD Dashboard
          </Text>
          <Text className={`text-lg mt-2 ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Welcome back! Role: {userRole}
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Quick Actions
          </Text>
          <View className="space-y-4">
            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-blue-900'
              }`}>
                👥 Manage Faculty
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                View and manage department faculty members
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-green-900'
              }`}>
                📊 Department Reports
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                Generate and view department analytics
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-purple-900'
              }`}>
                🎯 Strategic Planning
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Department goals and strategic initiatives
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Department Overview */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Department Overview
          </Text>
          <View className={`p-4 rounded-xl ${
            isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-50'
          }`}>
            <View className="flex-row justify-between items-center mb-3">
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Total Faculty Members
              </Text>
              <Text className={`text-lg font-semibold ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                25
              </Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Total Students
              </Text>
              <Text className={`text-lg font-semibold ${
                isDarkColorScheme ? 'text-green-400' : 'text-green-600'
              }`}>
                450
              </Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Active Courses
              </Text>
              <Text className={`text-lg font-semibold ${
                isDarkColorScheme ? 'text-purple-400' : 'text-purple-600'
              }`}>
                18
              </Text>
            </View>
          </View>
        </View>

        {/* Recent Activities */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Recent Activities
          </Text>
          <View className="space-y-3">
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-blue-900/20' : 'bg-blue-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-blue-300' : 'text-blue-800'
              }`}>
                📝 New faculty member Dr. Smith joined the department
              </Text>
            </View>
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-green-900/20' : 'bg-green-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-green-300' : 'text-green-800'
              }`}>
                🎓 Department achieved 95% student satisfaction rating
              </Text>
            </View>
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-yellow-900/20' : 'bg-yellow-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-yellow-300' : 'text-yellow-800'
              }`}>
                ⚠️ Monthly budget review meeting scheduled for Friday
              </Text>
            </View>
          </View>
        </View>

        {/* Key Metrics */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Key Metrics
          </Text>
          <View className="flex-row space-x-4">
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                95%
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                Student Satisfaction
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-green-400' : 'text-green-600'
              }`}>
                87%
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                Faculty Retention
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-purple-400' : 'text-purple-600'
              }`}>
                92%
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Course Completion
              </Text>
            </View>
          </View>
        </View>

        {/* Upcoming Events */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Upcoming Events
          </Text>
          <View className={`p-4 rounded-xl ${
            isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-50'
          }`}>
            <Text className={`text-base ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              📅 Faculty Meeting - Tomorrow, 10:00 AM
            </Text>
            <Text className={`text-base mt-2 ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              🎓 Graduation Ceremony - Next Friday, 2:00 PM
            </Text>
            <Text className={`text-base mt-2 ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'
            }`}>
              📊 Annual Review - Next Month, 15th
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
