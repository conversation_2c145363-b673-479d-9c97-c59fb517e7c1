import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface BuildingProps {
  size?: number;
  color?: string;
}

export function Building({ size = 24, color = "currentColor" }: BuildingProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M3 21h18M5 21V7l8-4v18M19 21V10l-6-3M9 9h.01M9 12h.01M9 15h.01"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}
