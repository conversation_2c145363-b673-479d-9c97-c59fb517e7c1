import React, { useState } from "react";
import { Alert, AppState, View, Text, TextInput, TouchableOpacity } from "react-native";
import { supabase } from "../lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";

// Tells Supabase Auth to continuously refresh the session automatically if
// the app is in the foreground. When this is added, you will continue to receive
// `onAuthStateChange` events with the `TOKEN_REFRESHED` or `SIGNED_OUT` event
// if the user's session is terminated. This should only be registered once.
AppState.addEventListener("change", (state) => {
  if (state === "active") {
    supabase.auth.startAutoRefresh();
  } else {
    supabase.auth.stopAutoRefresh();
  }
});

export default function Auth() {
  const { isDarkColorScheme } = useColorScheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  async function signInWithEmail() {
    if (!email || !password) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    setLoading(true);
    const { error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });

    if (error) Alert.alert(error.message);
    setLoading(false);
  }

  async function signUpWithEmail() {
    if (!email || !password) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    setLoading(true);
    const {
      data: { session },
      error,
    } = await supabase.auth.signUp({
      email: email,
      password: password,
    });

    if (error) Alert.alert(error.message);
    if (!session)
      Alert.alert("Please check your inbox for email verification!");
    setLoading(false);
  }

  return (
    <View className={`flex-1 p-6 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}>
      <View className="flex-1 justify-center">
        <View className="mb-8">
          <Text className={`text-2xl font-bold mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Welcome Back
          </Text>
          <Text className={`text-base ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Sign in to your account or create a new one
          </Text>
        </View>

        <View className="mb-6">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Email
          </Text>
          <TextInput
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
            className={`w-full py-4 px-4 rounded-xl text-lg ${
              isDarkColorScheme 
                ? 'bg-gray-800 text-white border border-gray-700' 
                : 'bg-gray-50 text-gray-900 border border-gray-200'
            }`}
            autoCapitalize="none"
            keyboardType="email-address"
          />
        </View>

        <View className="mb-8">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Password
          </Text>
          <TextInput
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
            className={`w-full py-4 px-4 rounded-xl text-lg ${
              isDarkColorScheme 
                ? 'bg-gray-800 text-white border border-gray-700' 
                : 'bg-gray-50 text-gray-900 border border-gray-200'
            }`}
            secureTextEntry
            autoCapitalize="none"
          />
        </View>

        <View className="space-y-4">
          <TouchableOpacity
            onPress={signInWithEmail}
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center ${
              loading 
                ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {loading ? "Signing In..." : "Sign In"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={signUpWithEmail}
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center border-2 ${
              isDarkColorScheme 
                ? 'border-gray-600 bg-transparent' 
                : 'border-gray-300 bg-transparent'
            }`}
            activeOpacity={0.8}
          >
            <Text className={`font-semibold text-lg ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              {loading ? "Creating Account..." : "Create Account"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
