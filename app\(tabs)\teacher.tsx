import { useEffect, useState } from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRole } from "~/lib/utils";

export default function TeacherScreen() {
  const { isDarkColorScheme } = useColorScheme();
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    async function loadRole() {
      const role = await getUserRole();
      setUserRole(role);
    }
    loadRole();
  }, []);

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
    >
      <ScrollView className="flex-1 px-6">
        {/* Header */}
        <View className="mt-6 mb-8">
          <Text className={`text-3xl font-bold ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Teacher Dashboard
          </Text>
          <Text className={`text-lg mt-2 ${
            isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Welcome back! Role: {userRole}
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Quick Actions
          </Text>
          <View className="space-y-4">
            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-blue-900'
              }`}>
                👥 My Classes
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                View and manage your assigned classes
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-green-900'
              }`}>
                📝 Grade Assignments
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                Grade student submissions and assignments
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-xl ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-lg font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-purple-900'
              }`}>
                📊 Attendance
              </Text>
              <Text className={`text-sm mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Mark and view student attendance
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Today's Schedule */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Today&apos;s Schedule
          </Text>
          <View className={`p-4 rounded-xl ${
            isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-50'
          }`}>
            <View className="flex-row justify-between items-center mb-3">
              <Text className={`text-base font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-gray-900'
              }`}>
                9:00 AM - Mathematics 101
              </Text>
              <Text className={`text-sm ${
                isDarkColorScheme ? 'text-green-400' : 'text-green-600'
              }`}>
                Room 201
              </Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className={`text-base font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-gray-900'
              }`}>
                11:00 AM - Physics Lab
              </Text>
              <Text className={`text-sm ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                Lab 3
              </Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className={`text-base font-medium ${
                isDarkColorScheme ? 'text-white' : 'text-gray-900'
              }`}>
                2:00 PM - Office Hours
              </Text>
              <Text className={`text-sm ${
                isDarkColorScheme ? 'text-purple-400' : 'text-purple-600'
              }`}>
                Office 105
              </Text>
            </View>
          </View>
        </View>

        {/* Pending Tasks */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Pending Tasks
          </Text>
          <View className="space-y-3">
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-red-300' : 'text-red-800'
              }`}>
                ⚠️ Grade 15 Math assignments (Due: Today)
              </Text>
            </View>
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-yellow-900/20' : 'bg-yellow-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-yellow-300' : 'text-yellow-800'
              }`}>
                📝 Prepare Physics quiz (Due: Tomorrow)
              </Text>
            </View>
            <View className={`p-3 rounded-lg ${
              isDarkColorScheme ? 'bg-blue-900/20' : 'bg-blue-50'
            }`}>
              <Text className={`text-base ${
                isDarkColorScheme ? 'text-blue-300' : 'text-blue-800'
              }`}>
                📊 Submit attendance report (Due: Friday)
              </Text>
            </View>
          </View>
        </View>

        {/* Stats */}
        <View className="mb-8">
          <Text className={`text-xl font-semibold mb-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            This Week
          </Text>
          <View className="flex-row space-x-4">
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-blue-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                4
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-blue-700'
              }`}>
                Classes
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-green-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-green-400' : 'text-green-600'
              }`}>
                45
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-green-700'
              }`}>
                Students
              </Text>
            </View>
            <View className={`flex-1 p-4 rounded-xl ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-purple-50'
            }`}>
              <Text className={`text-2xl font-bold text-center ${
                isDarkColorScheme ? 'text-purple-400' : 'text-purple-600'
              }`}>
                8
              </Text>
              <Text className={`text-sm text-center mt-1 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-purple-700'
              }`}>
                Hours
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
