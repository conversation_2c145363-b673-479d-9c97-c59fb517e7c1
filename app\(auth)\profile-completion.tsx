import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  Dimensions,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { ChevronDown } from "~/lib/icons/ChevronDown";
import { Phone } from "~/lib/icons/Phone";
import { Shield } from "~/lib/icons/Shield";
import { User } from "~/lib/icons/User";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import { updateProfile, type UserRole } from "~/lib/utils";

const { height } = Dimensions.get("window");

export default function ProfileCompletion() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [loading, setLoading] = useState(false);

  // Form state
  const [fullName, setFullName] = useState("");
  const [phone, setPhone] = useState("");
  const [selectedGender, setSelectedGender] = useState<string>("male");
  const [selectedRole, setSelectedRole] = useState<UserRole>("HOD");

  const genders = [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  const roles: { value: UserRole; label: string }[] = [
    { value: "HOD", label: "Head of Department" },
    { value: "teacher", label: "Teacher" },
    { value: "student", label: "Student" },
  ];

  async function completeProfile() {
    if (!fullName.trim()) {
      Alert.alert("Error", "Please enter your full name");
      return;
    }

    if (!phone.trim()) {
      Alert.alert("Error", "Please enter your phone number");
      return;
    }

    if (phone.length < 10) {
      Alert.alert("Error", "Please enter a valid phone number");
      return;
    }

    setLoading(true);
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        Alert.alert("Error", "No authenticated user found");
        return;
      }

      // Create or update profile with complete information
      const result = await updateProfile(user.id, {
        full_name: fullName.trim(),
        phone: phone.trim(),
        gender: selectedGender,
        role: [selectedRole],
        onboarding_completed: true,
        isverify: selectedRole === "student", // Students are auto-verified, others need super admin approval
      });

      if (!result.success) {
        Alert.alert("Error", result.error || "Failed to update profile");
        return;
      }

      // Handle different role redirections
      if (selectedRole === "HOD") {
        Alert.alert(
          "Profile Completed",
          "Your profile has been completed successfully. Please select your department.",
          [
            {
              text: "Continue",
              onPress: () => {
                router.replace("/(auth)/department-selection");
              },
            },
          ]
        );
      } else if (selectedRole === "student") {
        // Students are auto-verified and can go directly to the main app
        Alert.alert(
          "Profile Completed",
          "Your profile has been completed successfully. Welcome to the app!",
          [
            {
              text: "Continue",
              onPress: () => router.replace("/(tabs)"),
            },
          ]
        );
      } else {
        // For teachers, go directly to pending approval
        Alert.alert(
          "Profile Submitted",
          "Your profile has been submitted for approval. You will be notified once a super admin verifies your account.",
          [
            {
              text: "OK",
              onPress: () => router.replace("/(auth)/pending-approval"),
            },
          ]
        );
      }
    } catch (err) {
      console.error("Profile completion error:", err);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }

  return (
    <SafeAreaView
      className={`flex-1 ${isDarkColorScheme ? "bg-gray-900" : "bg-white"}`}
    >
      <ScrollView
        className="flex-1 px-6"
        contentContainerStyle={{ minHeight: height * 0.9 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View className="mt-8 mb-8">
          <View className="items-center mb-6">
            <View
              className={`w-20 h-20 rounded-full items-center justify-center ${
                isDarkColorScheme ? "bg-blue-600" : "bg-blue-500"
              }`}
            >
              <User size={32} color="white" />
            </View>
          </View>

          <Text
            className={`text-3xl font-bold text-center ${
              isDarkColorScheme ? "text-white" : "text-gray-900"
            }`}
          >
            Complete Your Profile
          </Text>
          <Text
            className={`text-lg text-center mt-2 ${
              isDarkColorScheme ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Please provide your details for verification
          </Text>
        </View>

        {/* Form */}
        <View className="space-y-6">
          {/* Full Name */}
          <View>
            <Text
              className={`text-lg font-medium mb-2 ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              Full Name
            </Text>
            <View
              className={`flex-row items-center px-4 py-4 rounded-xl border ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <User
                size={20}
                color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
              />
              <TextInput
                value={fullName}
                onChangeText={setFullName}
                placeholder="Enter your full name"
                placeholderTextColor={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                className={`flex-1 ml-3 text-lg ${
                  isDarkColorScheme ? "text-white" : "text-gray-900"
                }`}
              />
            </View>
          </View>

          {/* Phone */}
          <View>
            <Text
              className={`text-lg font-medium mb-2 ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              Phone Number
            </Text>
            <View
              className={`flex-row items-center px-4 py-4 rounded-xl border ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <Phone
                size={20}
                color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
              />
              <TextInput
                value={phone}
                onChangeText={setPhone}
                placeholder="Enter your phone number"
                placeholderTextColor={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                keyboardType="phone-pad"
                className={`flex-1 ml-3 text-lg ${
                  isDarkColorScheme ? "text-white" : "text-gray-900"
                }`}
              />
            </View>
          </View>

          {/* Gender */}
          <View>
            <Text
              className={`text-lg font-medium mb-2 ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              Gender
            </Text>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <TouchableOpacity
                  className={`flex-row items-center justify-between px-4 py-4 rounded-xl border ${
                    isDarkColorScheme
                      ? "bg-gray-800 border-gray-700"
                      : "bg-gray-50 border-gray-200"
                  }`}
                  activeOpacity={0.8}
                >
                  <View className="flex-row items-center">
                    <User
                      size={20}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-lg ${
                        isDarkColorScheme ? "text-white" : "text-gray-900"
                      }`}
                    >
                      {genders.find((g) => g.value === selectedGender)?.label ||
                        "Select Gender"}
                    </Text>
                  </View>
                  <ChevronDown
                    size={20}
                    color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                  />
                </TouchableOpacity>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className={`w-full ${
                  isDarkColorScheme
                    ? "bg-gray-800 border-gray-700"
                    : "bg-white border-gray-200"
                }`}
              >
                {genders.map((gender) => (
                  <DropdownMenuItem
                    key={gender.value}
                    onPress={() => setSelectedGender(gender.value)}
                    className={`py-3 ${
                      selectedGender === gender.value ? "bg-blue-50" : ""
                    }`}
                  >
                    <Text
                      className={`text-lg ${
                        selectedGender === gender.value
                          ? "text-blue-600 font-semibold"
                          : "text-gray-900"
                      }`}
                    >
                      {gender.label}
                    </Text>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </View>

          {/* Role */}
          <View>
            <Text
              className={`text-lg font-medium mb-2 ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              Role
            </Text>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <TouchableOpacity
                  className={`flex-row items-center justify-between px-4 py-4 rounded-xl border ${
                    isDarkColorScheme
                      ? "bg-gray-800 border-gray-700"
                      : "bg-gray-50 border-gray-200"
                  }`}
                  activeOpacity={0.8}
                >
                  <View className="flex-row items-center">
                    <Shield
                      size={20}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-lg ${
                        isDarkColorScheme ? "text-white" : "text-gray-900"
                      }`}
                    >
                      {roles.find((r) => r.value === selectedRole)?.label ||
                        "Select Role"}
                    </Text>
                  </View>
                  <ChevronDown
                    size={20}
                    color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                  />
                </TouchableOpacity>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className={`w-full ${
                  isDarkColorScheme
                    ? "bg-gray-800 border-gray-700"
                    : "bg-white border-gray-200"
                }`}
              >
                {roles.map((role) => (
                  <DropdownMenuItem
                    key={role.value}
                    onPress={() => setSelectedRole(role.value)}
                    className={`py-3 ${
                      selectedRole === role.value ? "bg-blue-50" : ""
                    }`}
                  >
                    <Text
                      className={`text-lg ${
                        selectedRole === role.value
                          ? "text-blue-600 font-semibold"
                          : "text-gray-900"
                      }`}
                    >
                      {role.label}
                    </Text>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </View>
        </View>

        {/* Submit Button */}
        <View className="mt-8 mb-6">
          <TouchableOpacity
            onPress={completeProfile}
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center ${
              loading
                ? isDarkColorScheme
                  ? "bg-gray-700"
                  : "bg-gray-300"
                : isDarkColorScheme
                ? "bg-blue-600"
                : "bg-blue-500"
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {loading ? "Submitting..." : "Submit for Approval"}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
