import AsyncStorage from "@react-native-async-storage/async-storage";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export type UserRole = "student" | "teacher" | "HOD";

// Department interface matching the database schema
export interface Department {
  id: number;
  name: string;
  code: string;
  description: string | null;
  is_active: boolean;
  created_at: string | null;
  updated_at: string | null;
}

// HOD interface matching the database schema
export interface HOD {
  id: number;
  department_id: number;
  employee_id: number;
  is_active: boolean;
  created_at: string | null;
  updated_at: string | null;
  department?: Department;
}

// Database profile type matching the new schema
export interface Profile {
  id: string;
  updated_at: string | null;
  full_name: string | null;
  avatar_url: string | null;
  email: string | null;
  gender: string | null;
  phone: string | null;
  role: UserRole[] | null;
  onboarding_completed: boolean | null;
  isverify: boolean | null;
  department_id?: number | null;
}

// User role management - Updated to use database
export async function getUserRole(): Promise<UserRole | null> {
  try {
    const { supabase } = await import("./supabase");
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return null;

    const profile = await getProfile(user.id);
    return profile?.role?.[0] || null;
  } catch (error) {
    console.error("Error getting user role:", error);
    // Fallback to AsyncStorage for backward compatibility
    try {
      const role = await AsyncStorage.getItem("userRole");
      return role as UserRole | null;
    } catch (fallbackError) {
      console.error(
        "Error getting user role from AsyncStorage:",
        fallbackError
      );
      return null;
    }
  }
}

export async function setUserRole(role: UserRole): Promise<void> {
  try {
    // Keep AsyncStorage for backward compatibility during transition
    await AsyncStorage.setItem("userRole", role);
  } catch (error) {
    console.error("Error setting user role:", error);
  }
}

export async function clearUserRole(): Promise<void> {
  try {
    await AsyncStorage.removeItem("userRole");
  } catch (error) {
    console.error("Error clearing user role:", error);
  }
}

// New database-based role functions
export async function getUserRoles(): Promise<UserRole[]> {
  try {
    const { supabase } = await import("./supabase");
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return [];

    const profile = await getProfile(user.id);
    return profile?.role || [];
  } catch (error) {
    console.error("Error getting user roles:", error);
    return [];
  }
}

export async function hasRole(role: UserRole): Promise<boolean> {
  try {
    const roles = await getUserRoles();
    return roles.includes(role);
  } catch (error) {
    console.error("Error checking user role:", error);
    return false;
  }
}

export async function isSuperAdmin(): Promise<boolean> {
  try {
    const { supabase } = await import("./supabase");
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return false;

    // Check is_super_admin from raw_app_meta_data
    const isSuperAdmin = user.app_metadata?.is_super_admin === true;
    return isSuperAdmin;
  } catch (error) {
    console.error("Error checking super admin status:", error);
    return false;
  }
}

// Function to set super admin status (this would typically be done server-side)
export async function setSuperAdminStatus(
  userId: string,
  isSuperAdmin: boolean
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    // Note: This requires admin privileges and should be done server-side
    // This is a placeholder for the actual implementation
    // In practice, you would call a server-side function or use Supabase Admin API

    console.warn(
      "setSuperAdminStatus should be implemented server-side with admin privileges"
    );
    return {
      success: false,
      error: "This function requires server-side implementation",
    };
  } catch (error) {
    console.error("Error setting super admin status:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

// Database profile management functions
export async function createProfile(
  userId: string,
  email: string,
  role: UserRole[]
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase.from("profiles").insert({
      id: userId,
      email: email,
      role: role,
      onboarding_completed: false,
      isverify: false,
      updated_at: new Date().toISOString(),
    });

    if (error) {
      console.error("Error creating profile:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function getProfile(userId: string): Promise<Profile | null> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching profile:", error);
      return null;
    }

    return data as Profile;
  } catch (error) {
    console.error("Error fetching profile:", error);
    return null;
  }
}

export async function updateProfile(
  userId: string,
  updates: Partial<Profile>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase
      .from("profiles")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (error) {
      console.error("Error updating profile:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

// Super admin functions
export async function getPendingVerifications(): Promise<
  (Profile & { department?: Department })[]
> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("profiles")
      .select(
        `
        *,
        department:department_id (*)
      `
      )
      .eq("onboarding_completed", true)
      .eq("isverify", false)
      .order("updated_at", { ascending: false });

    if (error) {
      console.error("Error fetching pending verifications:", error);
      return [];
    }

    return data as (Profile & { department?: Department })[];
  } catch (error) {
    console.error("Error fetching pending verifications:", error);
    return [];
  }
}

export async function approveUser(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    const { error } = await supabase
      .from("profiles")
      .update({
        isverify: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (error) {
      console.error("Error approving user:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error approving user:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function rejectUser(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    // Delete the profile and auth user
    const { error: profileError } = await supabase
      .from("profiles")
      .delete()
      .eq("id", userId);

    if (profileError) {
      console.error("Error rejecting user:", profileError);
      return { success: false, error: profileError.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error rejecting user:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

// Department management functions
export async function getDepartments(): Promise<Department[]> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("department")
      .select("*")
      .eq("is_active", true)
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching departments:", error);
      return [];
    }

    return data as Department[];
  } catch (error) {
    console.error("Error fetching departments:", error);
    return [];
  }
}

export async function getDepartmentById(
  id: number
): Promise<Department | null> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("department")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching department:", error);
      return null;
    }

    return data as Department;
  } catch (error) {
    console.error("Error fetching department:", error);
    return null;
  }
}

export async function assignHODToDepartment(
  userId: string,
  departmentId: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const { supabase } = await import("./supabase");

    // First, update the profile with department_id
    const { error: profileError } = await supabase
      .from("profiles")
      .update({
        department_id: departmentId,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (profileError) {
      console.error("Error updating profile with department:", profileError);
      return { success: false, error: profileError.message };
    }

    // Then, create HOD record (assuming employee_id is the user id for now)
    const { error: hodError } = await supabase.from("hod").insert({
      department_id: departmentId,
      employee_id: userId,
      is_active: true,
    });

    if (hodError) {
      console.error("Error creating HOD record:", hodError);
      return { success: false, error: hodError.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error assigning HOD to department:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function getHODDepartment(
  userId: string
): Promise<Department | null> {
  try {
    const { supabase } = await import("./supabase");

    const { data, error } = await supabase
      .from("hod")
      .select(
        `
        *,
        department:department_id (*)
      `
      )
      .eq("employee_id", userId)
      .eq("is_active", true)
      .single();

    if (error) {
      console.error("Error fetching HOD department:", error);
      return null;
    }

    return data?.department as Department;
  } catch (error) {
    console.error("Error fetching HOD department:", error);
    return null;
  }
}
