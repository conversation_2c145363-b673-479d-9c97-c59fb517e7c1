@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --background: 229 57% 100%;
    --foreground: 229 63% 4%;
    --muted: 229 12% 86%;
    --muted-foreground: 229 10% 37%;
    --popover: 0 0% 99%;
    --popover-foreground: 229 63% 3%;
    --card: 0 0% 99%;
    --card-foreground: 229 63% 3%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --primary: 229 100% 62%;
    --primary-foreground: 0 0% 100%;
    --secondary: 229 20% 90%;
    --secondary-foreground: 229 20% 30%;
    --accent: 229 28% 85%;
    --accent-foreground: 229 28% 25%;
    --destructive: 3 100% 50%;
    --destructive-foreground: 3 0% 100%;
    --ring: 229 100% 62%;
    --chart-1: 229 100% 62%;
    --chart-2: 229 20% 90%;
    --chart-3: 229 28% 85%;
    --chart-4: 229 20% 93%;
    --chart-5: 229 103% 62%;
    --radius: 0.5rem;
  }


  .dark {
    --background: 229 41% 4%;
    --foreground: 229 23% 99%;
    --muted: 229 12% 14%;
    --muted-foreground: 229 10% 63%;
    --popover: 229 41% 5%;
    --popover-foreground: 0 0% 100%;
    --card: 229 41% 5%;
    --card-foreground: 0 0% 100%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --primary: 229 100% 62%;
    --primary-foreground: 0 0% 100%;
    --secondary: 229 14% 8%;
    --secondary-foreground: 229 14% 68%;
    --accent: 229 23% 17%;
    --accent-foreground: 229 23% 77%;
    --destructive: 3 89% 54%;
    --destructive-foreground: 0 0% 100%;
    --ring: 229 100% 62%;
    --chart-1: 229 100% 62%;
    --chart-2: 229 14% 8%;
    --chart-3: 229 23% 17%;
    --chart-4: 229 14% 11%;
    --chart-5: 229 103% 62%;
  }
}
