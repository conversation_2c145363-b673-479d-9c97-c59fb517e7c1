import { Session } from "@supabase/supabase-js";
import { useCallback, useEffect, useState } from "react";
import { Alert, Text, TextInput, TouchableOpacity, View } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRole, type UserRole } from "~/lib/utils";
import { supabase } from "../lib/supabase";
import Avatar from "./Avatar";

export default function Account({ session }: { session: Session }) {
  const { isDarkColorScheme } = useColorScheme();
  const [loading, setLoading] = useState(true);
  const [username, setUsername] = useState("");
  const [website, setWebsite] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("");
  const [userRole, setUserRole] = useState<UserRole | null>(null);

  const getProfile = useCallback(async () => {
    try {
      setLoading(true);
      if (!session?.user) throw new Error("No user on the session!");

      const { data, error, status } = await supabase
        .from("profiles")
        .select(`username, website, avatar_url`)
        .eq("id", session?.user.id)
        .single();
      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUsername(data.username);
        setWebsite(data.website);
        setAvatarUrl(data.avatar_url);
      }

      // Get user role from AsyncStorage
      const role = await getUserRole();
      setUserRole(role);
    } catch (error) {
      if (error instanceof Error) {
        Alert.alert(error.message);
      }
    } finally {
      setLoading(false);
    }
  }, [session]);

  useEffect(() => {
    if (session) getProfile();
  }, [session, getProfile]);

  async function updateProfile({
    username,
    website,
    avatar_url,
  }: {
    username: string;
    website: string;
    avatar_url: string;
  }) {
    try {
      setLoading(true);
      if (!session?.user) throw new Error("No user on the session!");

      const updates = {
        id: session?.user.id,
        username,
        website,
        avatar_url,
        updated_at: new Date(),
      };

      const { error } = await supabase.from("profiles").upsert(updates);

      if (error) {
        throw error;
      }
    } catch (error) {
      if (error instanceof Error) {
        Alert.alert(error.message);
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <View className={`flex-1 p-6 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}>
      <View className="flex-1">
        {/* Avatar Section */}
        <View className="items-center mb-8">
          <Avatar
            size={200}
            url={avatarUrl}
            onUpload={(url: string) => {
              setAvatarUrl(url);
              updateProfile({ username, website, avatar_url: url });
            }}
          />
        </View>

        {/* Profile Form */}
        <View className="mb-6">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Email
          </Text>
          <TextInput
            value={session?.user?.email || ""}
            editable={false}
            className={`w-full py-4 px-4 rounded-xl text-lg ${
              isDarkColorScheme 
                ? 'bg-gray-800 text-gray-400 border border-gray-700' 
                : 'bg-gray-100 text-gray-500 border border-gray-200'
            }`}
          />
        </View>

        <View className="mb-6">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Role
          </Text>
          <View className={`w-full py-4 px-4 rounded-xl ${
            isDarkColorScheme 
              ? 'bg-gray-800 border border-gray-700' 
              : 'bg-gray-100 border border-gray-200'
          }`}>
            <Text className={`text-lg capitalize ${
              isDarkColorScheme ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {userRole || 'Loading...'}
            </Text>
          </View>
        </View>

        <View className="mb-6">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Username
          </Text>
          <TextInput
            value={username || ""}
            onChangeText={(text) => setUsername(text)}
            placeholder="Enter your username"
            placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
            className={`w-full py-4 px-4 rounded-xl text-lg ${
              isDarkColorScheme 
                ? 'bg-gray-800 text-white border border-gray-700' 
                : 'bg-gray-50 text-gray-900 border border-gray-200'
            }`}
          />
        </View>

        <View className="mb-8">
          <Text className={`text-lg font-medium mb-2 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Website
          </Text>
          <TextInput
            value={website || ""}
            onChangeText={(text) => setWebsite(text)}
            placeholder="Enter your website"
            placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
            className={`w-full py-4 px-4 rounded-xl text-lg ${
              isDarkColorScheme 
                ? 'bg-gray-800 text-white border border-gray-700' 
                : 'bg-gray-50 text-gray-900 border border-gray-200'
            }`}
          />
        </View>

        {/* Action Buttons */}
        <View className="space-y-4">
          <TouchableOpacity
            onPress={() =>
              updateProfile({ username, website, avatar_url: avatarUrl })
            }
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center ${
              loading 
                ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {loading ? "Updating..." : "Update Profile"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => supabase.auth.signOut()}
            className={`w-full py-4 rounded-xl items-center border-2 ${
              isDarkColorScheme 
                ? 'border-red-600 bg-transparent' 
                : 'border-red-500 bg-transparent'
            }`}
            activeOpacity={0.8}
          >
            <Text className={`font-semibold text-lg ${
              isDarkColorScheme ? 'text-red-400' : 'text-red-600'
            }`}>
              Sign Out
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
