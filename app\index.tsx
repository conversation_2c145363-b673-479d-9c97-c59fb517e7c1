import { Session } from "@supabase/supabase-js";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, Text, View } from "react-native";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import { getProfile, isSuperAdmin, type Profile } from "~/lib/utils";

export default function App() {
  console.log("🚀 App component initialized");

  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSuper, setIsSuper] = useState<boolean>(false);
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();

  console.log("📊 Initial state:", {
    session: session?.user?.id || "null",
    profile: profile?.id || "null",
    loading,
    isSuper,
    isDarkColorScheme,
  });

  useEffect(() => {
    console.log("🔄 useEffect triggered - initializing auth");

    const initializeAuth = async () => {
      console.log("🔐 Starting auth initialization...");
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        console.log("📱 Session retrieved:", {
          hasSession: !!session,
          userId: session?.user?.id,
          userEmail: session?.user?.email,
          sessionExpiry: session?.expires_at,
          rawAppMetaData: session?.user?.app_metadata,
        });
        setSession(session);

        if (session?.user) {
          console.log("👤 User found, fetching profile...");
          const userProfile = await getProfile(session.user.id);
          console.log("📋 Profile retrieved:", {
            profileId: userProfile?.id,
            role: userProfile?.role,
            onboardingCompleted: userProfile?.onboarding_completed,
            isVerified: userProfile?.isverify,
            departmentId: userProfile?.department_id,
            fullProfile: userProfile,
          });
          setProfile(userProfile);

          console.log("🔍 Checking super admin status...");
          const superAdminStatus = await isSuperAdmin();
          console.log("👑 Super admin status:", superAdminStatus);
          setIsSuper(superAdminStatus);
        } else {
          console.log("❌ No user in session");
        }
      } catch (error) {
        console.error("💥 Error initializing auth:", error);
        console.error("Error details:", {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          name: error instanceof Error ? error.name : typeof error,
        });
      } finally {
        console.log(
          "✅ Auth initialization complete, setting loading to false"
        );
        setLoading(false);
      }
    };

    initializeAuth();

    console.log("👂 Setting up auth state change listener...");
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("🔄 Auth state changed:", {
        event,
        hasSession: !!session,
        userId: session?.user?.id,
        userEmail: session?.user?.email,
      });

      setSession(session);

      if (session?.user) {
        console.log("👤 User in auth state change, fetching profile...");
        const userProfile = await getProfile(session.user.id);
        console.log("📋 Profile from auth state change:", {
          profileId: userProfile?.id,
          role: userProfile?.role,
          onboardingCompleted: userProfile?.onboarding_completed,
          isVerified: userProfile?.isverify,
        });
        setProfile(userProfile);

        console.log("🔍 Checking super admin status in auth state change...");
        const superAdminStatus = await isSuperAdmin();
        console.log(
          "👑 Super admin status from auth state change:",
          superAdminStatus
        );
        setIsSuper(superAdminStatus);
      } else {
        console.log(
          "❌ No user in auth state change, clearing profile and super admin status"
        );
        setProfile(null);
        setIsSuper(false);
      }
      console.log("✅ Auth state change complete, setting loading to false");
      setLoading(false);
    });

    return () => {
      console.log("🧹 Cleaning up auth state change subscription");
      subscription.unsubscribe();
    };
  }, []);

  // Show loading screen while checking auth
  if (loading) {
    console.log("⏳ Showing loading screen");
    return (
      <View
        className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? "bg-gray-900" : "bg-white"
        }`}
      >
        <ActivityIndicator
          size="large"
          color={isDarkColorScheme ? "#3B82F6" : "#2563EB"}
        />
        <Text
          className={`mt-4 text-lg ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          Loading...
        </Text>
      </View>
    );
  }

  // If no session, redirect to auth welcome screen
  if (!session || !session.user) {
    console.log("🚪 No session or user found, redirecting to welcome screen");
    router.replace("/(auth)/welcome");
    return null;
  }

  // Check profile status and redirect accordingly
  console.log("🔍 Checking profile status and routing logic...");
  console.log("Profile check state:", {
    hasProfile: !!profile,
    isSuper,
    profileRole: profile?.role,
    onboardingCompleted: profile?.onboarding_completed,
    isVerified: profile?.isverify,
    departmentId: profile?.department_id,
  });

  if (profile) {
    console.log("📋 Profile exists, checking routing conditions...");

    // If user is super admin, bypass all other checks
    if (isSuper) {
      console.log("👑 User is super admin, redirecting to tabs");
      router.replace("/(tabs)");
      return null;
    }

    // If profile exists but onboarding not completed, redirect to profile completion
    if (!profile.onboarding_completed) {
      console.log(
        "📝 Onboarding not completed, redirecting to profile completion"
      );
      router.replace("/(auth)/profile-completion");
      return null;
    }

    // Check if HOD has selected department
    if (profile.role?.includes("HOD" as any)) {
      console.log("🏢 User is HOD, checking department selection...");
      if (!profile.department_id) {
        console.log(
          "🏢 HOD has no department selected, redirecting to department selection"
        );
        router.replace("/(auth)/department-selection");
        return null;
      } else {
        console.log("✅ HOD has department selected:", profile.department_id);
      }
    }

    // If onboarding completed but not verified (for HODs/teachers), redirect to pending approval
    const needsVerification =
      (profile.onboarding_completed &&
        !profile.isverify &&
        profile.role?.includes("HOD" as any)) ||
      profile.role?.includes("teacher" as any);

    console.log("🔐 Checking verification status:", {
      needsVerification,
      onboardingCompleted: profile.onboarding_completed,
      isVerified: profile.isverify,
      isHOD: profile.role?.includes("HOD" as any),
      isTeacher: profile.role?.includes("teacher" as any),
    });

    if (needsVerification) {
      console.log(
        "⏳ User needs verification, redirecting to pending approval"
      );
      router.replace("/(auth)/pending-approval");
      return null;
    }
  } else {
    console.log("❌ No profile found");
  }

  // If authenticated, redirect to tabs
  console.log("✅ All checks passed, redirecting to tabs");
  router.replace("/(tabs)");

  return (
    <View
      className={`flex-1 items-center justify-center ${
        isDarkColorScheme ? "bg-gray-900" : "bg-white"
      }`}
    >
      <Text
        className={`text-lg ${
          isDarkColorScheme ? "text-white" : "text-gray-900"
        }`}
      >
        Redirecting...
      </Text>
    </View>
  );
}
