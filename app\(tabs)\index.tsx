import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { Text, View } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { getUserRoles, isSuperAdmin, type UserRole } from "~/lib/utils";

export default function TabsIndex() {
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    async function loadUserData() {
      try {
        const roles = await getUserRoles();
        const adminStatus = await isSuperAdmin();
        setUserRoles(roles);
        setIsAdmin(adminStatus);
      } catch (error) {
        console.error("Error loading user data:", error);
      } finally {
        setLoading(false);
      }
    }

    loadUserData();
  }, []);

  if (loading) {
    return (
      <View
        className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? "bg-gray-900" : "bg-white"
        }`}
      >
        <Text
          className={`text-lg ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          Loading...
        </Text>
      </View>
    );
  }

  // Redirect based on roles
  if (isAdmin) {
    return <Redirect href="/(tabs)/admin" />;
  }

  const primaryRole = userRoles[0];

  // Redirect to role-specific screen
  switch (primaryRole) {
    case "student":
      return <Redirect href="/(tabs)/student" />;
    case "teacher":
      return <Redirect href="/(tabs)/teacher" />;
    case "HOD":
      return <Redirect href="/(tabs)/hod" />;
    default:
      return (
        <View
          className={`flex-1 items-center justify-center ${
            isDarkColorScheme ? "bg-gray-900" : "bg-white"
          }`}
        >
          <Text
            className={`text-lg ${
              isDarkColorScheme ? "text-white" : "text-gray-900"
            }`}
          >
            Invalid role. Please sign in again.
          </Text>
        </View>
      );
  }
}
