import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { ChevronDown } from "~/lib/icons/ChevronDown";
import { Building } from "~/lib/icons/Building";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import {
  assignHODToDepartment,
  getDepartments,
  type Department,
} from "~/lib/utils";

const { height } = Dimensions.get("window");

export default function DepartmentSelection() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingDepartments, setLoadingDepartments] = useState(true);

  useEffect(() => {
    loadDepartments();
  }, []);

  async function loadDepartments() {
    try {
      const departmentList = await getDepartments();
      setDepartments(departmentList);
    } catch (error) {
      console.error("Error loading departments:", error);
      Alert.alert("Error", "Failed to load departments");
    } finally {
      setLoadingDepartments(false);
    }
  }

  async function handleSubmit() {
    if (!selectedDepartment) {
      Alert.alert("Error", "Please select a department");
      return;
    }

    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        Alert.alert("Error", "No authenticated user found");
        return;
      }

      const result = await assignHODToDepartment(user.id, selectedDepartment.id);

      if (!result.success) {
        Alert.alert("Error", result.error || "Failed to assign department");
        return;
      }

      Alert.alert(
        "Department Selected",
        `You have been assigned to ${selectedDepartment.name}. Your request is now pending admin approval.`,
        [
          {
            text: "Continue",
            onPress: () => {
              router.replace("/(auth)/pending-approval");
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error submitting department selection:", error);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }

  return (
    <SafeAreaView
      className={`flex-1 ${isDarkColorScheme ? "bg-gray-900" : "bg-white"}`}
    >
      <ScrollView
        className="flex-1 px-6"
        contentContainerStyle={{ minHeight: height * 0.9 }}
      >
        {/* Header */}
        <View className="mt-8 mb-8">
          <View className="items-center mb-6">
            <Building
              size={64}
              color={isDarkColorScheme ? "#60a5fa" : "#3b82f6"}
            />
          </View>
          <Text
            className={`text-3xl font-bold text-center mb-4 ${
              isDarkColorScheme ? "text-white" : "text-gray-900"
            }`}
          >
            Select Department
          </Text>
          <Text
            className={`text-lg text-center ${
              isDarkColorScheme ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Choose the department you will be heading as HOD
          </Text>
        </View>

        {/* Department Selection */}
        <View className="mb-8">
          <Text
            className={`text-base font-medium mb-3 ${
              isDarkColorScheme ? "text-gray-200" : "text-gray-700"
            }`}
          >
            Department *
          </Text>
          
          {loadingDepartments ? (
            <View
              className={`p-4 rounded-xl border ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <Text
                className={`text-center ${
                  isDarkColorScheme ? "text-gray-300" : "text-gray-600"
                }`}
              >
                Loading departments...
              </Text>
            </View>
          ) : departments.length === 0 ? (
            <View
              className={`p-4 rounded-xl border ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <Text
                className={`text-center ${
                  isDarkColorScheme ? "text-gray-300" : "text-gray-600"
                }`}
              >
                No departments available
              </Text>
            </View>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <TouchableOpacity
                  className={`flex-row items-center justify-between p-4 rounded-xl border ${
                    isDarkColorScheme
                      ? "bg-gray-800 border-gray-700"
                      : "bg-white border-gray-200"
                  }`}
                  activeOpacity={0.7}
                >
                  <Text
                    className={`text-base ${
                      selectedDepartment
                        ? isDarkColorScheme
                          ? "text-white"
                          : "text-gray-900"
                        : isDarkColorScheme
                        ? "text-gray-400"
                        : "text-gray-500"
                    }`}
                  >
                    {selectedDepartment
                      ? `${selectedDepartment.name} (${selectedDepartment.code})`
                      : "Select a department"}
                  </Text>
                  <ChevronDown
                    size={20}
                    color={isDarkColorScheme ? "#9ca3af" : "#6b7280"}
                  />
                </TouchableOpacity>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {departments.map((department) => (
                  <DropdownMenuItem
                    key={department.id}
                    onPress={() => setSelectedDepartment(department)}
                  >
                    <View>
                      <Text className="font-medium">{department.name}</Text>
                      <Text className="text-sm text-gray-500">
                        Code: {department.code}
                      </Text>
                      {department.description && (
                        <Text className="text-sm text-gray-400 mt-1">
                          {department.description}
                        </Text>
                      )}
                    </View>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </View>

        {/* Selected Department Info */}
        {selectedDepartment && (
          <View
            className={`p-4 rounded-xl mb-8 ${
              isDarkColorScheme ? "bg-blue-900/20" : "bg-blue-50"
            }`}
          >
            <Text
              className={`text-lg font-semibold mb-2 ${
                isDarkColorScheme ? "text-blue-300" : "text-blue-800"
              }`}
            >
              Selected Department
            </Text>
            <Text
              className={`text-base font-medium ${
                isDarkColorScheme ? "text-blue-200" : "text-blue-700"
              }`}
            >
              {selectedDepartment.name}
            </Text>
            <Text
              className={`text-sm ${
                isDarkColorScheme ? "text-blue-300" : "text-blue-600"
              }`}
            >
              Department Code: {selectedDepartment.code}
            </Text>
            {selectedDepartment.description && (
              <Text
                className={`text-sm mt-2 ${
                  isDarkColorScheme ? "text-blue-300" : "text-blue-600"
                }`}
              >
                {selectedDepartment.description}
              </Text>
            )}
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={loading || !selectedDepartment || loadingDepartments}
          className={`py-4 rounded-xl ${
            loading || !selectedDepartment || loadingDepartments
              ? isDarkColorScheme
                ? "bg-gray-700"
                : "bg-gray-300"
              : "bg-blue-600"
          }`}
          activeOpacity={0.8}
        >
          <Text
            className={`text-center text-lg font-semibold ${
              loading || !selectedDepartment || loadingDepartments
                ? isDarkColorScheme
                  ? "text-gray-400"
                  : "text-gray-500"
                : "text-white"
            }`}
          >
            {loading ? "Submitting..." : "Request Access"}
          </Text>
        </TouchableOpacity>

        {/* Info Text */}
        <Text
          className={`text-center text-sm mt-6 ${
            isDarkColorScheme ? "text-gray-400" : "text-gray-500"
          }`}
        >
          After selecting your department, your request will be sent to the super admin for approval.
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}
