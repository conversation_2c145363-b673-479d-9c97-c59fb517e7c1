import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export function useCachedResources() {
  const [fontsLoaded] = useFonts({
    "IBM Plex Sans": require("../assets/fonts/IBMPlexSans-Regular.ttf"),
    "IBM Plex Sans Bold": require("../assets/fonts/IBMPlexSans-Bold.ttf"),
    "IBM Plex Sans Medium": require("../assets/fonts/IBMPlexSans-Medium.ttf"),
    Roboto: require("../assets/fonts/Roboto-Regular.ttf"),
    "Roboto Bold": require("../assets/fonts/Roboto-Bold.ttf"),
    "Roboto Medium": require("../assets/fonts/Roboto-Medium.ttf"),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  return fontsLoaded;
}
