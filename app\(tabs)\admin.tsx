import { useCallback, useEffect, useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Building } from "~/lib/icons/Building";
import { CheckCircle } from "~/lib/icons/CheckCircle";
import { Clock } from "~/lib/icons/Clock";
import { Mail } from "~/lib/icons/Mail";
import { Phone } from "~/lib/icons/Phone";
import { Shield } from "~/lib/icons/Shield";
import { User } from "~/lib/icons/User";
import { XCircle } from "~/lib/icons/XCircle";
import { useColorScheme } from "~/lib/useColorScheme";
import {
  approveUser,
  getPendingVerifications,
  rejectUser,
  type Department,
  type Profile,
} from "~/lib/utils";

export default function AdminScreen() {
  const { isDarkColorScheme } = useColorScheme();
  const [pendingUsers, setPendingUsers] = useState<
    (Profile & { department?: Department })[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadPendingUsers = useCallback(async () => {
    try {
      const users = await getPendingVerifications();
      setPendingUsers(users);
    } catch (error) {
      console.error("Error loading pending users:", error);
      Alert.alert("Error", "Failed to load pending verifications");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadPendingUsers();
  }, [loadPendingUsers]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadPendingUsers();
  }, [loadPendingUsers]);

  const handleApprove = async (userId: string, userName: string) => {
    Alert.alert(
      "Approve User",
      `Are you sure you want to approve ${userName}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Approve",
          style: "default",
          onPress: async () => {
            try {
              const result = await approveUser(userId);
              if (result.success) {
                Alert.alert("Success", "User approved successfully");
                loadPendingUsers(); // Refresh the list
              } else {
                Alert.alert("Error", result.error || "Failed to approve user");
              }
            } catch (error) {
              console.error("Error approving user:", error);
              Alert.alert("Error", "An unexpected error occurred");
            }
          },
        },
      ]
    );
  };

  const handleReject = async (userId: string, userName: string) => {
    Alert.alert(
      "Reject User",
      `Are you sure you want to reject ${userName}? This will permanently delete their account.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reject",
          style: "destructive",
          onPress: async () => {
            try {
              const result = await rejectUser(userId);
              if (result.success) {
                Alert.alert("Success", "User rejected and account deleted");
                loadPendingUsers(); // Refresh the list
              } else {
                Alert.alert("Error", result.error || "Failed to reject user");
              }
            } catch (error) {
              console.error("Error rejecting user:", error);
              Alert.alert("Error", "An unexpected error occurred");
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Unknown";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <SafeAreaView
      className={`flex-1 ${isDarkColorScheme ? "bg-gray-900" : "bg-white"}`}
    >
      <ScrollView
        className="flex-1 px-6"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View className="mt-6 mb-8">
          <Text
            className={`text-3xl font-bold ${
              isDarkColorScheme ? "text-white" : "text-gray-900"
            }`}
          >
            Super Admin Dashboard
          </Text>
          <Text
            className={`text-lg mt-2 ${
              isDarkColorScheme ? "text-gray-300" : "text-gray-600"
            }`}
          >
            Pending User Verifications
          </Text>
        </View>

        {/* Stats */}
        <View
          className={`p-4 rounded-xl mb-6 ${
            isDarkColorScheme ? "bg-gray-800" : "bg-blue-50"
          }`}
        >
          <View className="flex-row items-center">
            <Clock
              size={20}
              color={isDarkColorScheme ? "#60A5FA" : "#2563EB"}
            />
            <Text
              className={`ml-3 text-lg font-semibold ${
                isDarkColorScheme ? "text-blue-400" : "text-blue-600"
              }`}
            >
              {pendingUsers.length} Pending Approval
              {pendingUsers.length !== 1 ? "s" : ""}
            </Text>
          </View>
        </View>

        {/* Pending Users List */}
        {loading ? (
          <View className="items-center py-8">
            <Text
              className={`text-lg ${
                isDarkColorScheme ? "text-gray-300" : "text-gray-600"
              }`}
            >
              Loading...
            </Text>
          </View>
        ) : pendingUsers.length === 0 ? (
          <View className="items-center py-8">
            <CheckCircle
              size={48}
              color={isDarkColorScheme ? "#10B981" : "#059669"}
            />
            <Text
              className={`text-xl font-semibold mt-4 ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              All Caught Up!
            </Text>
            <Text
              className={`text-lg mt-2 ${
                isDarkColorScheme ? "text-gray-300" : "text-gray-600"
              }`}
            >
              No pending verifications
            </Text>
          </View>
        ) : (
          <View className="space-y-4">
            {pendingUsers.map((user) => (
              <View
                key={user.id}
                className={`p-4 rounded-xl border ${
                  isDarkColorScheme
                    ? "bg-gray-800 border-gray-700"
                    : "bg-white border-gray-200"
                }`}
              >
                {/* User Info */}
                <View className="mb-4">
                  <View className="flex-row items-center mb-3">
                    <User
                      size={20}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-lg font-semibold ${
                        isDarkColorScheme ? "text-white" : "text-gray-900"
                      }`}
                    >
                      {user.full_name || "No name provided"}
                    </Text>
                  </View>

                  <View className="flex-row items-center mb-2">
                    <Mail
                      size={16}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-base ${
                        isDarkColorScheme ? "text-gray-300" : "text-gray-700"
                      }`}
                    >
                      {user.email || "No email"}
                    </Text>
                  </View>

                  {user.phone && (
                    <View className="flex-row items-center mb-2">
                      <Phone
                        size={16}
                        color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                      />
                      <Text
                        className={`ml-3 text-base ${
                          isDarkColorScheme ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        {user.phone}
                      </Text>
                    </View>
                  )}

                  <View className="flex-row items-center mb-2">
                    <Shield
                      size={16}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-base ${
                        isDarkColorScheme ? "text-gray-300" : "text-gray-700"
                      }`}
                    >
                      Role: {user.role?.join(", ") || "No role specified"}
                    </Text>
                  </View>

                  {/* Department info for HODs */}
                  {user.role?.includes("HOD") && user.department && (
                    <View className="flex-row items-center mb-2">
                      <Building
                        size={16}
                        color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                      />
                      <Text
                        className={`ml-3 text-base ${
                          isDarkColorScheme ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Department: {user.department.name} (
                        {user.department.code})
                      </Text>
                    </View>
                  )}

                  {/* Show if HOD hasn't selected department */}
                  {user.role?.includes("HOD") && !user.department && (
                    <View className="flex-row items-center mb-2">
                      <Building
                        size={16}
                        color={isDarkColorScheme ? "#EF4444" : "#DC2626"}
                      />
                      <Text
                        className={`ml-3 text-base ${
                          isDarkColorScheme ? "text-red-400" : "text-red-600"
                        }`}
                      >
                        Department: Not selected
                      </Text>
                    </View>
                  )}

                  <View className="flex-row items-center mb-2">
                    <Clock
                      size={16}
                      color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                    />
                    <Text
                      className={`ml-3 text-sm ${
                        isDarkColorScheme ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      Submitted: {formatDate(user.updated_at)}
                    </Text>
                  </View>
                </View>

                {/* Action Buttons */}
                <View className="flex-row space-x-3">
                  <TouchableOpacity
                    onPress={() =>
                      handleApprove(
                        user.id,
                        user.full_name || user.email || "User"
                      )
                    }
                    className="flex-1 flex-row items-center justify-center py-3 rounded-lg bg-green-600"
                    activeOpacity={0.8}
                  >
                    <CheckCircle size={18} color="white" />
                    <Text className="ml-2 text-white font-semibold">
                      Approve
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() =>
                      handleReject(
                        user.id,
                        user.full_name || user.email || "User"
                      )
                    }
                    className="flex-1 flex-row items-center justify-center py-3 rounded-lg bg-red-600"
                    activeOpacity={0.8}
                  >
                    <XCircle size={18} color="white" />
                    <Text className="ml-2 text-white font-semibold">
                      Reject
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
